/* Modern Layout Styles */
.modern-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen', 'Ubuntu', 'Can<PERSON>ell', sans-serif;
}

/* Header */
.modern-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: #4f46e5;
}

.logo svg {
  color: #4f46e5;
}

.logo h1 {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(135deg, #4f46e5, #7c3aed);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.sidebar-toggle {
  background: none;
  border: none;
  padding: 0.5rem;
  border-radius: 0.5rem;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
  display: none;
}

.sidebar-toggle:hover {
  background: rgba(107, 114, 128, 0.1);
  color: #374151;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem 1rem;
  background: rgba(79, 70, 229, 0.1);
  border-radius: 2rem;
  border: 1px solid rgba(79, 70, 229, 0.2);
}

.user-avatar {
  width: 2rem;
  height: 2rem;
  background: linear-gradient(135deg, #4f46e5, #7c3aed);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 0.875rem;
}

.user-name {
  font-weight: 500;
  color: #374151;
  font-size: 0.875rem;
}

.logout-btn {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.logout-btn:hover {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

/* Main Content */
.modern-main {
  flex: 1;
  display: flex;
  min-height: 0;
}

/* Sidebar */
.modern-sidebar {
  width: 280px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-right: 1px solid rgba(255, 255, 255, 0.2);
  padding: 1.5rem;
  overflow-y: auto;
  transition: all 0.3s ease;
}

.modern-sidebar.show {
  transform: translateX(0);
}

/* Content */
.modern-content {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
  background: rgba(255, 255, 255, 0.05);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .modern-header {
    padding: 1rem;
  }
  
  .sidebar-toggle {
    display: block;
  }
  
  .modern-sidebar {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 200;
    transform: translateX(-100%);
    box-shadow: 2px 0 20px rgba(0, 0, 0, 0.1);
  }
  
  .modern-content {
    padding: 1.5rem;
  }
  
  .user-name {
    display: none;
  }
}

@media (max-width: 640px) {
  .modern-header {
    padding: 0.75rem;
  }
  
  .logo h1 {
    font-size: 1.25rem;
  }
  
  .modern-content {
    padding: 1rem;
  }
  
  .user-info {
    padding: 0.25rem 0.5rem;
  }
  
  .logout-btn {
    padding: 0.375rem 0.75rem;
    font-size: 0.8rem;
  }
  
  .logout-btn span {
    display: none;
  }
}

/* Animations */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modern-layout {
  animation: slideIn 0.5s ease-out;
}

/* Scrollbar Styling */
.modern-sidebar::-webkit-scrollbar,
.modern-content::-webkit-scrollbar {
  width: 6px;
}

.modern-sidebar::-webkit-scrollbar-track,
.modern-content::-webkit-scrollbar-track {
  background: transparent;
}

.modern-sidebar::-webkit-scrollbar-thumb,
.modern-content::-webkit-scrollbar-thumb {
  background: rgba(107, 114, 128, 0.3);
  border-radius: 3px;
}

.modern-sidebar::-webkit-scrollbar-thumb:hover,
.modern-content::-webkit-scrollbar-thumb:hover {
  background: rgba(107, 114, 128, 0.5);
}
