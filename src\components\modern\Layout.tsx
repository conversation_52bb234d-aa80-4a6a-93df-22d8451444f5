import React from 'react';
import { signOut } from 'firebase/auth';
import { auth } from '../../firebase';
import type { User, UserData } from '../../types';
import './Layout.css';

interface LayoutProps {
  user: User;
  userData: UserData;
  children: React.ReactNode;
  showSidebar?: boolean;
  onToggleSidebar?: () => void;
  sidebar?: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({
  user,
  userData,
  children,
  showSidebar = false,
  onToggleSidebar,
  sidebar
}) => {
  const displayName = userData?.name || user.email;

  const handleLogout = async (): Promise<void> => {
    try {
      await signOut(auth);
    } catch (error) {
      console.error("Error al cerrar sesión:", error);
    }
  };

  return (
    <div className="modern-layout">
      {/* Header */}
      <header className="modern-header">
        <div className="header-left">
          <div className="logo">
            <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M10 4H4C2.89543 4 2 4.89543 2 6V18C2 19.1046 2.89543 20 4 20H20C21.1046 20 22 19.1046 22 18V8C22 6.89543 21.1046 6 20 6H12L10 4Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
            <h1>Cloud Drive</h1>
          </div>
          {onToggleSidebar && (
            <button 
              className="sidebar-toggle"
              onClick={onToggleSidebar}
              aria-label={showSidebar ? "Ocultar sidebar" : "Mostrar sidebar"}
            >
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M3 12H21M3 6H21M3 18H21" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </button>
          )}
        </div>
        
        <div className="header-right">
          <div className="user-info">
            <div className="user-avatar">
              {displayName.charAt(0).toUpperCase()}
            </div>
            <span className="user-name">{displayName}</span>
          </div>
          
          <button className="logout-btn" onClick={handleLogout}>
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M9 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H9" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M16 17L21 12L16 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M21 12H9" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
            Cerrar sesión
          </button>
        </div>
      </header>

      {/* Main Content */}
      <div className="modern-main">
        {/* Sidebar */}
        {sidebar && (
          <aside className={`modern-sidebar ${showSidebar ? 'show' : ''}`}>
            {sidebar}
          </aside>
        )}
        
        {/* Content */}
        <main className="modern-content">
          {children}
        </main>
      </div>
    </div>
  );
};

export default Layout;
