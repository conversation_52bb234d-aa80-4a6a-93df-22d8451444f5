.drive-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.drive-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e0e0e0;
}

.logout-btn {
  background-color: #f44336;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
}

.view-toggle {
  display: flex;
  margin-bottom: 20px;
}

.toggle-btn {
  padding: 10px 20px;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  cursor: pointer;
}

.toggle-btn.active {
  background-color: #4285f4;
  color: white;
  border-color: #4285f4;
}

.toggle-btn:first-child {
  border-radius: 4px 0 0 4px;
}

.toggle-btn:last-child {
  border-radius: 0 4px 4px 0;
}

.drive-content {
  display: flex;
  gap: 20px;
}

.sidebar {
  width: 250px;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.main-content {
  flex: 1;
}

.folders-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.folders-list li {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.folder-btn {
  flex: 1;
  text-align: left;
  padding: 8px 12px;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
}

.folder-btn.active {
  background-color: #e3f2fd;
  border-color: #2196f3;
}

.delete-btn {
  background-color: transparent;
  border: none;
  cursor: pointer;
  margin-left: 8px;
  color: #f44336;
}

.upload-section, .create-folder-section {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.upload-form {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.upload-area {
  border: 2px dashed #ccc;
  padding: 20px;
  text-align: center;
  border-radius: 4px;
  position: relative;
}

.upload-form.drag-active .upload-area {
  border-color: #4285f4;
  background-color: #e3f2fd;
}

.file-input {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  opacity: 0;
  cursor: pointer;
}

.file-label {
  display: block;
  cursor: pointer;
}

.upload-btn, .folder-form button {
  padding: 8px 16px;
  background-color: #4285f4;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.upload-btn:disabled, .folder-form button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.folder-form {
  display: flex;
  gap: 10px;
}

.folder-form input {
  flex: 1;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.files-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.file-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  border-bottom: 1px solid #e0e0e0;
  transition: background-color 0.2s;
}

.file-item:hover {
  background-color: #f5f5f5;
}

.file-item.shared {
  background-color: #e8f0fe;
}

.file-item.shared:hover {
  background-color: #d2e3fc;
}

.file-info {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 0; /* Para que funcione text-overflow */
  cursor: pointer;
  transition: background-color 0.2s;
}

.file-info:hover .file-name {
  color: #4285f4;
  text-decoration: underline;
}

.file-name.clickable {
  color: #333;
  transition: color 0.2s, text-decoration 0.2s;
}

.file-icon {
  font-size: 20px;
  margin-right: 10px;
  display: inline-block;
}

.file-name {
  font-weight: 500;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.file-details {
  display: flex;
  font-size: 12px;
  color: #666;
  flex-wrap: wrap;
  gap: 10px;
}

.file-owner, .file-permission {
  font-size: 12px;
  color: #666;
  margin-top: 2px;
}

.file-actions {
  display: flex;
  gap: 8px;
}

.view-btn, .share-btn, .delete-btn, .download-btn {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  padding: 5px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.view-btn:hover, .share-btn:hover, .delete-btn:hover, .download-btn:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.download-btn {
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* Responsive para la lista de archivos */
@media (max-width: 768px) {
  .file-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .file-info {
    width: 100%;
    margin-bottom: 10px;
  }
  
  .file-actions {
    width: 100%;
    justify-content: flex-end;
  }
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.share-modal {
  background-color: white;
  padding: 20px;
  border-radius: 4px;
  width: 400px;
  max-width: 90%;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
}

.form-group input, .form-group select {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.modal-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.cancel-btn {
  background-color: #f5f5f5;
  color: #333;
  border: 1px solid #ddd;
}

.error-message {
  color: #f44336;
  margin-bottom: 10px;
}

.empty-message {
  text-align: center;
  color: #757575;
  padding: 20px;
}

.loading-spinner {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  animation: spin 2s linear infinite;
  margin: 20px auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Estilos para la barra de búsqueda y filtros */
.search-filter-container {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.search-bar {
  display: flex;
  margin-bottom: 15px;
  position: relative;
}

.search-input {
  flex: 1;
  padding: 10px 40px 10px 15px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.clear-search-btn {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #999;
  cursor: pointer;
  font-size: 16px;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.filter-group {
  display: flex;
  flex-direction: column;
  min-width: 150px;
}

.filter-group label {
  margin-bottom: 5px;
  font-size: 14px;
  color: #666;
}

.filter-select {
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
}

.sort-direction-btn {
  padding: 8px 12px;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sort-direction-btn:hover {
  background-color: #e9e9e9;
}

/* Añadir estilos para elementos clickables */
.clickable {
  cursor: pointer;
  color: #1a73e8;
}

.clickable:hover {
  text-decoration: underline;
}

.view-btn {
  background-color: #4285f4;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  margin-right: 8px;
}

.view-btn:hover {
  background-color: #3367d6;
}

/* Estilos responsivos mejorados */
@media (max-width: 992px) {
  .drive-container {
    padding: 15px;
  }
  
  .drive-content {
    flex-direction: column;
  }
  
  .sidebar {
    width: 100%;
    margin-bottom: 20px;
  }
  
  .main-content {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .drive-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .drive-header h1 {
    font-size: 1.5rem;
  }
  
  .logout-btn {
    align-self: flex-end;
  }
  
  .file-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .file-list-item {
    flex-direction: column;
    padding: 10px;
  }
  
  .file-info {
    width: 100%;
    margin-bottom: 10px;
  }
  
  .file-actions {
    width: 100%;
    justify-content: space-between;
  }
}

@media (max-width: 480px) {
  .view-toggle {
    flex-direction: column;
    width: 100%;
  }
  
  .toggle-btn {
    width: 100%;
    border-radius: 0;
  }
  
  .toggle-btn:first-child {
    border-radius: 4px 4px 0 0;
  }
  
  .toggle-btn:last-child {
    border-radius: 0 0 4px 4px;
  }
  
  .file-grid {
    grid-template-columns: 1fr;
  }
  
  .search-filter-container {
    padding: 10px;
  }
  
  .filter-options {
    flex-direction: column;
  }
  
  .filter-group {
    width: 100%;
    margin-bottom: 10px;
  }
}

/* Estilos para el ícono de descarga animado */
.download-icon {
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.animate-arrow {
  animation: bounce 1s infinite;
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(2px);
  }
}

/* Estilos para el botón de descarga en la lista de archivos */
.download-btn {
  background: none;
  border: none;
  padding: 5px;
  border-radius: 50%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
}

.download-btn:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

/* Estilos para el ícono de enlace animado */
.link-icon {
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.animate-link-path {
  animation: linkWiggle 1s infinite;
}

@keyframes linkWiggle {
  0%, 100% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(-5deg);
  }
  50% {
    transform: rotate(0deg);
  }
  75% {
    transform: rotate(5deg);
  }
}

/* Estilos para el botón de compartir en la lista de archivos */
.share-btn {
  background: none;
  border: none;
  padding: 5px;
  border-radius: 50%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
}

.share-btn:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

/* Estilos para el ícono de eliminar animado */
.delete-icon {
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.animate-lid {
  animation: liftLid 0.5s ease-in-out;
  transform-origin: center;
  transform-box: fill-box;
}

.animate-bin {
  animation: shakeBin 0.5s ease-in-out;
}

.animate-line {
  animation: moveLine 0.5s ease-in-out;
}

@keyframes liftLid {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-1.5px);
  }
}

@keyframes shakeBin {
  0%, 100% {
    transform: translateY(0);
  }
  25% {
    transform: translateY(0.5px);
  }
  75% {
    transform: translateY(0.5px);
  }
}

@keyframes moveLine {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(0.5px);
  }
}

/* Estilos para el botón de eliminar en la lista de archivos */
.delete-btn {
  background: none;
  border: none;
  padding: 5px;
  border-radius: 50%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
  color: #f44336;
}

.delete-btn:hover {
  background-color: rgba(244, 67, 54, 0.1);
}
