import React from 'react';
import type { DriveFile, SharedFile } from '../../types';
import './FileCard.css';

interface FileCardProps {
  file: DriveFile;
  onView: (file: DriveFile) => void;
  onDelete: (file: DriveFile) => void;
  onShare?: (file: DriveFile) => void;
  onDownload?: (file: DriveFile) => void;
}

const FileCard: React.FC<FileCardProps> = ({
  file,
  onView,
  onDelete,
  onShare,
  onDownload
}) => {
  const isSharedFile = 'isShared' in file && file.isShared;
  const fileName = isSharedFile ? (file as SharedFile).fileName : file.name;
  const fileUrl = isSharedFile ? (file as SharedFile).fileUrl : file.url;

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (timestamp: any): string => {
    if (!timestamp) return 'Fecha desconocida';
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    return date.toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getFileIcon = (type: string): JSX.Element => {
    switch (type) {
      case 'image':
        return (
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect x="3" y="3" width="18" height="18" rx="2" ry="2" stroke="currentColor" strokeWidth="2"/>
            <circle cx="8.5" cy="8.5" r="1.5" stroke="currentColor" strokeWidth="2"/>
            <path d="M21 15L16 10L5 21" stroke="currentColor" strokeWidth="2"/>
          </svg>
        );
      case 'document':
        return (
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M14 2H6C4.89543 2 4 2.89543 4 4V20C4 21.1046 4.89543 22 6 22H18C19.1046 22 20 21.1046 20 20V8L14 2Z" stroke="currentColor" strokeWidth="2"/>
            <path d="M14 2V8H20" stroke="currentColor" strokeWidth="2"/>
            <path d="M16 13H8" stroke="currentColor" strokeWidth="2"/>
            <path d="M16 17H8" stroke="currentColor" strokeWidth="2"/>
            <path d="M10 9H8" stroke="currentColor" strokeWidth="2"/>
          </svg>
        );
      case 'video':
        return (
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <polygon points="23 7 16 12 23 17 23 7" stroke="currentColor" strokeWidth="2"/>
            <rect x="1" y="5" width="15" height="14" rx="2" ry="2" stroke="currentColor" strokeWidth="2"/>
          </svg>
        );
      case 'audio':
        return (
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M9 18V5L21 3V16" stroke="currentColor" strokeWidth="2"/>
            <circle cx="6" cy="18" r="3" stroke="currentColor" strokeWidth="2"/>
            <circle cx="18" cy="16" r="3" stroke="currentColor" strokeWidth="2"/>
          </svg>
        );
      default:
        return (
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M14 2H6C4.89543 2 4 2.89543 4 4V20C4 21.1046 4.89543 22 6 22H18C19.1046 22 20 21.1046 20 20V8L14 2Z" stroke="currentColor" strokeWidth="2"/>
            <path d="M14 2V8H20" stroke="currentColor" strokeWidth="2"/>
          </svg>
        );
    }
  };

  const getTypeColor = (type: string): string => {
    switch (type) {
      case 'image': return '#10b981';
      case 'document': return '#3b82f6';
      case 'video': return '#f59e0b';
      case 'audio': return '#8b5cf6';
      default: return '#6b7280';
    }
  };

  const handleDownload = () => {
    if (onDownload) {
      onDownload(file);
    } else {
      // Fallback to direct download
      const link = document.createElement('a');
      link.href = fileUrl;
      link.download = fileName;
      link.target = '_blank';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  return (
    <div className="file-card">
      <div className="file-card-header">
        <div 
          className="file-icon"
          style={{ color: getTypeColor(file.type) }}
        >
          {getFileIcon(file.type)}
        </div>
        
        {isSharedFile && (
          <div className="shared-badge">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M16 21V19C16 17.9391 15.5786 16.9217 14.8284 16.1716C14.0783 15.4214 13.0609 15 12 15H5C3.93913 15 2.92172 15.4214 2.17157 16.1716C1.42143 16.9217 1 17.9391 1 19V21" stroke="currentColor" strokeWidth="2"/>
              <circle cx="8.5" cy="7" r="4" stroke="currentColor" strokeWidth="2"/>
              <path d="M20 8V14" stroke="currentColor" strokeWidth="2"/>
              <path d="M23 11H17" stroke="currentColor" strokeWidth="2"/>
            </svg>
          </div>
        )}
      </div>

      <div className="file-card-content">
        <h3 className="file-name" title={fileName}>
          {fileName}
        </h3>
        
        <div className="file-meta">
          <span className="file-size">{formatFileSize(file.size)}</span>
          <span className="file-date">{formatDate(file.createdAt)}</span>
        </div>

        {isSharedFile && (
          <div className="shared-info">
            <span className="shared-by">
              Por: {(file as SharedFile).ownerName || (file as SharedFile).ownerEmail}
            </span>
          </div>
        )}
      </div>

      <div className="file-card-actions">
        <button
          className="action-btn primary"
          onClick={() => onView(file)}
          title="Ver archivo"
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z" stroke="currentColor" strokeWidth="2"/>
            <circle cx="12" cy="12" r="3" stroke="currentColor" strokeWidth="2"/>
          </svg>
        </button>

        <button
          className="action-btn secondary"
          onClick={handleDownload}
          title="Descargar archivo"
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15" stroke="currentColor" strokeWidth="2"/>
            <path d="M7 10L12 15L17 10" stroke="currentColor" strokeWidth="2"/>
            <path d="M12 15V3" stroke="currentColor" strokeWidth="2"/>
          </svg>
        </button>

        {onShare && !isSharedFile && (
          <button
            className="action-btn secondary"
            onClick={() => onShare(file)}
            title="Compartir archivo"
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M4 12V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V12" stroke="currentColor" strokeWidth="2"/>
              <path d="M16 6L12 2L8 6" stroke="currentColor" strokeWidth="2"/>
              <path d="M12 2V15" stroke="currentColor" strokeWidth="2"/>
            </svg>
          </button>
        )}

        <button
          className="action-btn danger"
          onClick={() => onDelete(file)}
          title="Eliminar archivo"
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M3 6H5H21" stroke="currentColor" strokeWidth="2"/>
            <path d="M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6H19Z" stroke="currentColor" strokeWidth="2"/>
          </svg>
        </button>
      </div>
    </div>
  );
};

export default FileCard;
