import React, { useState, useEffect } from 'react';
import { onAuthStateChanged } from 'firebase/auth';
import { doc, getDoc } from 'firebase/firestore';
import { auth, db } from './firebase';

// Importar ambas versiones
import Drive from './Drive'; // Versión original
import ModernDrivePage from './pages/ModernDrivePage'; // Nueva versión moderna
import Auth from './Auth';

// Tipos
import type { User, UserData } from './types';

function App() {
  const [user, setUser] = useState<User | null>(null);
  const [userData, setUserData] = useState<UserData | null>(null);
  const [loading, setLoading] = useState(true);
  const [useModernUI, setUseModernUI] = useState(true); // Toggle para cambiar entre versiones

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      if (firebaseUser) {
        const userObj: User = {
          uid: firebaseUser.uid,
          email: firebaseUser.email || '',
          displayName: firebaseUser.displayName || undefined
        };
        setUser(userObj);

        // Obtener datos adicionales del usuario
        try {
          const userDocRef = doc(db, 'users', firebaseUser.uid);
          const userDoc = await getDoc(userDocRef);
          
          if (userDoc.exists()) {
            setUserData(userDoc.data() as UserData);
          } else {
            setUserData({ email: firebaseUser.email || '' });
          }
        } catch (error) {
          console.error('Error al obtener datos del usuario:', error);
          setUserData({ email: firebaseUser.email || '' });
        }
      } else {
        setUser(null);
        setUserData(null);
      }
      setLoading(false);
    });

    return () => unsubscribe();
  }, []);

  if (loading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        color: 'white',
        fontFamily: 'system-ui, sans-serif'
      }}>
        <div style={{ textAlign: 'center' }}>
          <div style={{
            width: '50px',
            height: '50px',
            border: '3px solid rgba(255,255,255,0.3)',
            borderTop: '3px solid white',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
            margin: '0 auto 20px'
          }}></div>
          <p>Cargando...</p>
        </div>
      </div>
    );
  }

  if (!user || !userData) {
    return <Auth />;
  }

  return (
    <div>
      {/* Toggle para cambiar entre versiones */}
      <div style={{
        position: 'fixed',
        top: '20px',
        right: '20px',
        zIndex: 1000,
        background: 'rgba(255, 255, 255, 0.9)',
        padding: '10px 15px',
        borderRadius: '25px',
        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
        backdropFilter: 'blur(10px)'
      }}>
        <label style={{
          display: 'flex',
          alignItems: 'center',
          gap: '10px',
          cursor: 'pointer',
          fontSize: '14px',
          fontWeight: '500'
        }}>
          <input
            type="checkbox"
            checked={useModernUI}
            onChange={(e) => setUseModernUI(e.target.checked)}
            style={{ margin: 0 }}
          />
          UI Moderna
        </label>
      </div>

      {/* Renderizar la versión seleccionada */}
      {useModernUI ? (
        <ModernDrivePage user={user} userData={userData} />
      ) : (
        <Drive user={user} userData={userData} />
      )}
    </div>
  );
}

export default App;

// Estilos CSS para la animación de carga (agregar al CSS global)
/*
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
*/
