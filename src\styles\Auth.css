.auth-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f5f5f5;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.auth-card {
  width: 100%;
  max-width: 400px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 30px;
}

.auth-header {
  text-align: center;
  margin-bottom: 24px;
}

.auth-header h1 {
  margin: 0;
  color: #4285f4;
  font-size: 28px;
}

.auth-header p {
  margin-top: 8px;
  color: #5f6368;
  font-size: 16px;
}

.auth-tabs {
  display: flex;
  margin-bottom: 24px;
  border-bottom: 1px solid #e0e0e0;
}

.auth-tab {
  flex: 1;
  background: none;
  border: none;
  padding: 12px;
  font-size: 16px;
  font-weight: 500;
  color: #5f6368;
  cursor: pointer;
  transition: all 0.2s;
}

.auth-tab.active {
  color: #4285f4;
  border-bottom: 2px solid #4285f4;
}

.auth-tab:hover {
  background-color: #f8f9fa;
}

.auth-form {
  display: flex;
  flex-direction: column;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #202124;
}

.form-group input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #dadce0;
  border-radius: 4px;
  font-size: 16px;
  transition: border-color 0.2s;
}

.form-group input:focus {
  outline: none;
  border-color: #4285f4;
}

.auth-button {
  margin-top: 8px;
  padding: 12px;
  background-color: #4285f4;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.auth-button:hover {
  background-color: #3367d6;
}

.auth-error {
  background-color: #fdeded;
  color: #d93025;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 16px;
  font-size: 14px;
}

@media (max-width: 480px) {
  .auth-card {
    border-radius: 0;
    box-shadow: none;
    padding: 20px;
    width: 100%;
    max-width: 100%;
    height: 100vh;
    display: flex;
    flex-direction: column;
  }
  
  .auth-tabs {
    margin-bottom: 20px;
  }
  
  .auth-tab {
    padding: 10px;
  }
  
  .auth-form {
    flex: 1;
    display: flex;
    flex-direction: column;
  }
  
  .auth-form-fields {
    flex: 1;
  }
  
  .auth-button {
    margin-top: auto;
    padding: 15px;
  }
}
