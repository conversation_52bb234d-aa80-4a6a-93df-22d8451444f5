.file-viewer-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.file-viewer-container {
  background-color: white;
  border-radius: 8px;
  width: 90%;
  height: 90%;
  max-width: 1200px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.file-viewer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #e0e0e0;
}

.file-viewer-header h3 {
  margin: 0;
  font-size: 18px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 80%;
}

.close-button {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
}

.close-button:hover {
  color: #000;
}

.file-viewer-content {
  flex: 1;
  overflow: auto;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  position: relative;
}

.file-viewer-footer {
  padding: 15px 20px;
  border-top: 1px solid #e0e0e0;
  display: flex;
  justify-content: flex-end;
}

.download-button {
  background-color: #4285f4;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  text-decoration: none;
  font-size: 14px;
}

.download-button:hover {
  background-color: #3367d6;
}

/* Contenedores específicos por tipo */
.image-container {
  max-width: 100%;
  max-height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.image-container img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.video-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.video-container video {
  max-width: 100%;
  max-height: 100%;
}

.audio-container {
  width: 100%;
  padding: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.audio-container audio {
  width: 100%;
}

.pdf-container {
  width: 100%;
  height: 100%;
}

.unsupported-file {
  text-align: center;
  padding: 30px;
}

.loading-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 10px 20px;
  border-radius: 4px;
}

/* Estilos para la vista previa de código */
.code-container {
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.code-preview {
  padding: 15px;
  font-family: 'Courier New', Courier, monospace;
  font-size: 14px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-break: break-all;
}

/* Estilos para el contenedor de Google Docs */
.google-docs-container {
  width: 100%;
  height: 100%;
}

/* Responsive */
@media (max-width: 768px) {
  .file-viewer-container {
    width: 95%;
    height: 95%;
  }
}

@media (max-width: 480px) {
  .file-viewer-container {
    width: 100%;
    height: 100%;
    border-radius: 0;
  }
  
  .file-viewer-header {
    padding: 10px 15px;
  }
  
  .file-viewer-content {
    padding: 10px;
  }
  
  .file-viewer-footer {
    padding: 10px 15px;
  }
}
