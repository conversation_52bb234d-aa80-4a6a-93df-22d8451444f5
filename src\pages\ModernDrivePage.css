/* Modern Drive Page Styles */
.modern-drive-content {
  width: 100%;
  max-width: 100%;
}

/* Sidebar Content */
.modern-sidebar-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  height: 100%;
}

.sidebar-section {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 0.75rem;
  padding: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-section h3 {
  color: #1f2937;
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.sidebar-section h3::before {
  content: '';
  width: 3px;
  height: 1rem;
  background: linear-gradient(135deg, #4f46e5, #7c3aed);
  border-radius: 2px;
}

/* Content Section */
.content-section {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Breadcrumb */
.breadcrumb {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 1.5rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 0.75rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.875rem;
}

.breadcrumb-btn {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.breadcrumb-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
}

.breadcrumb-separator {
  color: rgba(255, 255, 255, 0.5);
  font-weight: 300;
}

.breadcrumb-current {
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
}

/* Search and Filter Styling Override */
.content-section .search-filter-container {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 1rem;
  padding: 1.5rem;
}

.content-section .search-filter-container input,
.content-section .search-filter-container select {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: #1f2937;
}

.content-section .search-filter-container input:focus,
.content-section .search-filter-container select:focus {
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .modern-sidebar-content {
    gap: 1.5rem;
  }
  
  .sidebar-section {
    padding: 1.25rem;
  }
  
  .content-section {
    gap: 1.25rem;
  }
}

@media (max-width: 768px) {
  .modern-sidebar-content {
    gap: 1rem;
  }
  
  .sidebar-section {
    padding: 1rem;
  }
  
  .sidebar-section h3 {
    font-size: 0.9rem;
  }
  
  .content-section {
    gap: 1rem;
  }
  
  .breadcrumb {
    padding: 0.75rem 1rem;
    font-size: 0.8rem;
  }
}

@media (max-width: 640px) {
  .breadcrumb {
    flex-wrap: wrap;
    gap: 0.25rem;
  }
  
  .breadcrumb-current {
    word-break: break-all;
  }
}

/* Animation */
@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.modern-drive-content {
  animation: slideInFromRight 0.4s ease-out;
}

/* Custom scrollbar for sidebar */
.modern-sidebar-content::-webkit-scrollbar {
  width: 4px;
}

.modern-sidebar-content::-webkit-scrollbar-track {
  background: transparent;
}

.modern-sidebar-content::-webkit-scrollbar-thumb {
  background: rgba(107, 114, 128, 0.3);
  border-radius: 2px;
}

.modern-sidebar-content::-webkit-scrollbar-thumb:hover {
  background: rgba(107, 114, 128, 0.5);
}

/* Enhanced focus styles for accessibility */
.breadcrumb-btn:focus {
  outline: 2px solid rgba(79, 70, 229, 0.5);
  outline-offset: 2px;
}

.breadcrumb-btn:focus:not(:focus-visible) {
  outline: none;
}

/* Loading and error states specific to modern page */
.modern-drive-content .loading-state,
.modern-drive-content .error-state,
.modern-drive-content .empty-state {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border-radius: 1rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Hover effects for interactive elements */
.sidebar-section:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.15);
  transition: all 0.3s ease;
}

.breadcrumb:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}
