import React, { useState, useMemo, useEffect } from "react";
import { useFiles, useSharedWithMe } from "../hooks/useFiles";
import { useFolders } from "../hooks/useFolders";
import { uploadFile, deleteFile } from "../services/fileService";
import { createFolder, deleteFolder } from "../services/folderService";
import { shareFile } from "../services/shareService";
import { SUCCESS_MESSAGES } from "../constants/permissions";

// Modern Components
import Layout from "../components/modern/Layout";
import TabNavigation from "../components/modern/TabNavigation";
import FileGrid from "../components/modern/FileGrid";
import FolderList from "../components/FolderList";
import UploadForm from "../components/UploadForm";
import CreateFolderForm from "../components/CreateFolderForm";
import ShareModal from "../components/ShareModal";
import SearchAndFilter from "../components/SearchAndFilter";
import FileViewer from "../components/FileViewer";

// Types
import type {
  DriveProps,
  DriveFile,
  SharedFile,
  ViewMode,
  FilterType,
  SortBy,
  SortDirection,
  Permission,
  SearchAndFilterState
} from "../types";

// Styles
import "./ModernDrivePage.css";

const ModernDrivePage: React.FC<DriveProps> = ({ user, userData }) => {
  // Main state
  const [selectedFolderId, setSelectedFolderId] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<ViewMode>("my-files");
  const [isUploading, setIsUploading] = useState(false);
  const [isCreatingFolder, setIsCreatingFolder] = useState(false);
  const [selectedFileForShare, setSelectedFileForShare] = useState<DriveFile | null>(null);
  const [showShareModal, setShowShareModal] = useState(false);
  const [selectedFileForView, setSelectedFileForView] = useState<DriveFile | null>(null);
  const [showSidebar, setShowSidebar] = useState(window.innerWidth > 1024);

  // Search and filter state
  const [searchAndFilter, setSearchAndFilter] = useState<SearchAndFilterState>({
    searchTerm: "",
    filterType: "all",
    sortBy: "name",
    sortDirection: "asc"
  });

  // Custom hooks
  const { folders, loading: loadingFolders, error: foldersError } = useFolders(user);
  const { files, loading: loadingFiles, error: filesError } = useFiles(user, selectedFolderId);
  const { sharedFiles, loading: loadingShared, error: sharedError } = useSharedWithMe(user);

  // Memoized filtered and sorted files
  const filteredAndSortedFiles = useMemo(() => {
    let result = [...files];
    
    if (searchAndFilter.searchTerm) {
      const searchLower = searchAndFilter.searchTerm.toLowerCase();
      result = result.filter(file => 
        file.name.toLowerCase().includes(searchLower)
      );
    }
    
    if (searchAndFilter.filterType !== "all") {
      result = result.filter(file => file.type === searchAndFilter.filterType);
    }
    
    result.sort((a, b) => {
      let valueA: any, valueB: any;
      
      if (searchAndFilter.sortBy === "name") {
        valueA = a.name.toLowerCase();
        valueB = b.name.toLowerCase();
      } else if (searchAndFilter.sortBy === "size") {
        valueA = a.size || 0;
        valueB = b.size || 0;
      } else if (searchAndFilter.sortBy === "createdAt") {
        valueA = a.createdAt ? (a.createdAt.toDate ? a.createdAt.toDate() : new Date(a.createdAt)) : new Date(0);
        valueB = b.createdAt ? (b.createdAt.toDate ? b.createdAt.toDate() : new Date(b.createdAt)) : new Date(0);
      } else if (searchAndFilter.sortBy === "type") {
        valueA = a.type || "";
        valueB = b.type || "";
      }
      
      if (searchAndFilter.sortDirection === "asc") {
        return valueA > valueB ? 1 : -1;
      } else {
        return valueA < valueB ? 1 : -1;
      }
    });
    
    return result;
  }, [files, searchAndFilter]);

  // Memoized filtered and sorted shared files
  const filteredAndSortedSharedFiles = useMemo(() => {
    let result = [...sharedFiles];
    
    if (searchAndFilter.searchTerm) {
      const searchLower = searchAndFilter.searchTerm.toLowerCase();
      result = result.filter(file => 
        file.fileName.toLowerCase().includes(searchLower)
      );
    }
    
    if (searchAndFilter.filterType !== "all") {
      result = result.filter(file => file.type === searchAndFilter.filterType);
    }
    
    result.sort((a, b) => {
      let valueA: any, valueB: any;
      
      if (searchAndFilter.sortBy === "name") {
        valueA = a.fileName.toLowerCase();
        valueB = b.fileName.toLowerCase();
      } else if (searchAndFilter.sortBy === "size") {
        valueA = a.size || 0;
        valueB = b.size || 0;
      } else if (searchAndFilter.sortBy === "createdAt") {
        valueA = a.createdAt ? (a.createdAt.toDate ? a.createdAt.toDate() : new Date(a.createdAt)) : new Date(0);
        valueB = b.createdAt ? (b.createdAt.toDate ? b.createdAt.toDate() : new Date(b.createdAt)) : new Date(0);
      } else if (searchAndFilter.sortBy === "type") {
        valueA = a.type || "";
        valueB = b.type || "";
      }
      
      if (searchAndFilter.sortDirection === "asc") {
        return valueA > valueB ? 1 : -1;
      } else {
        return valueA < valueB ? 1 : -1;
      }
    });
    
    return result;
  }, [sharedFiles, searchAndFilter]);

  // Window resize effect
  useEffect(() => {
    const handleResize = () => {
      setShowSidebar(window.innerWidth > 1024);
    };
    
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Event handlers
  const handleUpload = async (file: File): Promise<void> => {
    if (!file) return;
    
    setIsUploading(true);
    try {
      await uploadFile(file, user, selectedFolderId);
      alert(SUCCESS_MESSAGES.UPLOAD);
    } catch (error: any) {
      console.error("Error al subir archivo:", error);
      alert("Error al subir archivo: " + error.message);
    } finally {
      setIsUploading(false);
    }
  };

  const handleCreateFolder = async (folderName: string): Promise<void> => {
    if (!folderName.trim()) return;
    
    setIsCreatingFolder(true);
    try {
      await createFolder(folderName, user);
      alert(SUCCESS_MESSAGES.FOLDER_CREATE);
    } catch (error: any) {
      console.error("Error al crear carpeta:", error);
      alert("Error al crear carpeta: " + error.message);
    } finally {
      setIsCreatingFolder(false);
    }
  };

  const handleDeleteFile = async (file: DriveFile): Promise<void> => {
    if (!window.confirm(`¿Estás seguro de que quieres eliminar "${file.name || (file as SharedFile).fileName}"?`)) {
      return;
    }
    
    try {
      const message = await deleteFile(file, user);
      alert(message);
    } catch (error: any) {
      console.error("Error al eliminar archivo:", error);
      alert("Error al eliminar archivo: " + error.message);
    }
  };

  const handleDeleteFolder = async (folderId: string): Promise<void> => {
    if (!window.confirm("¿Estás seguro de que quieres eliminar esta carpeta y todo su contenido?")) {
      return;
    }
    
    try {
      const message = await deleteFolder(folderId, user);
      alert(message);
    } catch (error: any) {
      console.error("Error al eliminar carpeta:", error);
      alert("Error al eliminar carpeta: " + error.message);
    }
  };

  const handleShareFile = async (file: DriveFile, email: string, permission: Permission): Promise<any> => {
    try {
      const result = await shareFile(file, email, permission, user);
      alert(result.message || SUCCESS_MESSAGES.SHARE);
      return result;
    } catch (error: any) {
      console.error("Error al compartir archivo:", error);
      alert("Error al compartir archivo: " + error.message);
      throw error;
    }
  };

  // Modal handlers
  const openShareModal = (file: DriveFile): void => {
    setSelectedFileForShare(file);
    setShowShareModal(true);
  };

  const closeShareModal = (): void => {
    setSelectedFileForShare(null);
    setShowShareModal(false);
  };

  const openFileViewer = (file: DriveFile): void => {
    setSelectedFileForView(file);
  };

  const closeFileViewer = (): void => {
    setSelectedFileForView(null);
  };

  // State update helpers
  const updateSearchTerm = (searchTerm: string): void => {
    setSearchAndFilter(prev => ({ ...prev, searchTerm }));
  };

  const updateFilterType = (filterType: FilterType): void => {
    setSearchAndFilter(prev => ({ ...prev, filterType }));
  };

  const updateSortBy = (sortBy: SortBy): void => {
    setSearchAndFilter(prev => ({ ...prev, sortBy }));
  };

  const updateSortDirection = (sortDirection: SortDirection): void => {
    setSearchAndFilter(prev => ({ ...prev, sortDirection }));
  };

  // Sidebar content
  const sidebarContent = viewMode === "my-files" ? (
    <div className="modern-sidebar-content">
      <div className="sidebar-section">
        <h3>Carpetas</h3>
        <FolderList 
          folders={folders}
          selectedFolderId={selectedFolderId}
          onSelectFolder={setSelectedFolderId}
          onDeleteFolder={handleDeleteFolder}
          loading={loadingFolders}
          error={foldersError}
        />
      </div>
      
      <div className="sidebar-section">
        <h3>Crear carpeta</h3>
        <CreateFolderForm 
          onCreateFolder={handleCreateFolder}
          isCreating={isCreatingFolder}
        />
      </div>
      
      <div className="sidebar-section">
        <h3>Subir archivo</h3>
        <UploadForm 
          onUpload={handleUpload}
          selectedFolderId={selectedFolderId}
          isUploading={isUploading}
        />
      </div>
    </div>
  ) : null;

  return (
    <Layout
      user={user}
      userData={userData}
      showSidebar={showSidebar}
      onToggleSidebar={() => setShowSidebar(!showSidebar)}
      sidebar={sidebarContent}
    >
      <div className="modern-drive-content">
        <TabNavigation
          activeTab={viewMode}
          onTabChange={setViewMode}
        />

        <div className="content-section">
          {selectedFolderId && viewMode === "my-files" && (
            <div className="breadcrumb">
              <button 
                className="breadcrumb-btn"
                onClick={() => setSelectedFolderId(null)}
              >
                Raíz
              </button>
              <span className="breadcrumb-separator">/</span>
              <span className="breadcrumb-current">
                {folders.find(f => f.id === selectedFolderId)?.name || "Carpeta"}
              </span>
            </div>
          )}

          <SearchAndFilter 
            searchTerm={searchAndFilter.searchTerm}
            setSearchTerm={updateSearchTerm}
            filterType={searchAndFilter.filterType}
            setFilterType={updateFilterType}
            sortBy={searchAndFilter.sortBy}
            setSortBy={updateSortBy}
            sortDirection={searchAndFilter.sortDirection}
            setSortDirection={updateSortDirection}
          />

          <FileGrid
            files={viewMode === "my-files" ? filteredAndSortedFiles : filteredAndSortedSharedFiles}
            onView={openFileViewer}
            onDelete={handleDeleteFile}
            onShare={viewMode === "my-files" ? openShareModal : undefined}
            loading={viewMode === "my-files" ? loadingFiles : loadingShared}
            error={viewMode === "my-files" ? filesError : sharedError}
            emptyMessage={
              searchAndFilter.searchTerm || searchAndFilter.filterType !== "all" 
                ? "No se encontraron archivos con los filtros aplicados" 
                : (viewMode === "my-files"
                  ? (selectedFolderId 
                    ? "No hay archivos en esta carpeta" 
                    : "No hay archivos en la raíz")
                  : "No hay archivos compartidos contigo")
            }
          />
        </div>
      </div>

      {/* Modals */}
      {showShareModal && selectedFileForShare && (
        <ShareModal 
          file={selectedFileForShare}
          onShare={handleShareFile}
          onClose={closeShareModal}
        />
      )}
      
      {selectedFileForView && (
        <FileViewer 
          file={selectedFileForView}
          onClose={closeFileViewer}
        />
      )}
    </Layout>
  );
};

export default ModernDrivePage;
