import React from 'react';
import FileCard from './FileCard';
import type { DriveFile } from '../../types';
import './FileGrid.css';

interface FileGridProps {
  files: DriveFile[];
  onView: (file: DriveFile) => void;
  onDelete: (file: DriveFile) => void;
  onShare?: (file: DriveFile) => void;
  onDownload?: (file: DriveFile) => void;
  loading?: boolean;
  error?: string | null;
  emptyMessage?: string;
}

const FileGrid: React.FC<FileGridProps> = ({
  files,
  onView,
  onDelete,
  onShare,
  onDownload,
  loading = false,
  error = null,
  emptyMessage = "No hay archivos para mostrar"
}) => {
  if (loading) {
    return (
      <div className="file-grid-container">
        <div className="loading-state">
          <div className="loading-spinner">
            <div className="spinner"></div>
          </div>
          <p>Cargando archivos...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="file-grid-container">
        <div className="error-state">
          <div className="error-icon">
            <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"/>
              <path d="M15 9L9 15" stroke="currentColor" strokeWidth="2"/>
              <path d="M9 9L15 15" stroke="currentColor" strokeWidth="2"/>
            </svg>
          </div>
          <h3>Error al cargar archivos</h3>
          <p>{error}</p>
        </div>
      </div>
    );
  }

  if (files.length === 0) {
    return (
      <div className="file-grid-container">
        <div className="empty-state">
          <div className="empty-icon">
            <svg width="64" height="64" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M10 4H4C2.89543 4 2 4.89543 2 6V18C2 19.1046 2.89543 20 4 20H20C21.1046 20 22 19.1046 22 18V8C22 6.89543 21.1046 6 20 6H12L10 4Z" stroke="currentColor" strokeWidth="2"/>
              <path d="M12 10V16" stroke="currentColor" strokeWidth="2"/>
              <path d="M9 13H15" stroke="currentColor" strokeWidth="2"/>
            </svg>
          </div>
          <h3>No hay archivos</h3>
          <p>{emptyMessage}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="file-grid-container">
      <div className="file-grid-header">
        <h3>
          {files.length} {files.length === 1 ? 'archivo' : 'archivos'}
        </h3>
        <div className="grid-controls">
          {/* Aquí se pueden agregar controles adicionales como vista de lista/cuadrícula */}
        </div>
      </div>
      
      <div className="file-grid">
        {files.map((file) => (
          <FileCard
            key={file.id}
            file={file}
            onView={onView}
            onDelete={onDelete}
            onShare={onShare}
            onDownload={onDownload}
          />
        ))}
      </div>
    </div>
  );
};

export default FileGrid;
