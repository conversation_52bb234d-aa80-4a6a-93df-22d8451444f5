/* File Grid Styles */
.file-grid-container {
  width: 100%;
}

.file-grid-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding: 0 0.5rem;
}

.file-grid-header h3 {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0;
}

.grid-controls {
  display: flex;
  gap: 0.5rem;
}

/* File Grid */
.file-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
  padding: 0.5rem;
}

/* Loading State */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  color: rgba(255, 255, 255, 0.8);
}

.loading-spinner {
  margin-bottom: 1rem;
}

.spinner {
  width: 3rem;
  height: 3rem;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-state p {
  font-size: 1rem;
  margin: 0;
}

/* Error State */
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
}

.error-icon {
  color: #ef4444;
  margin-bottom: 1rem;
}

.error-state h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: rgba(255, 255, 255, 0.9);
}

.error-state p {
  font-size: 0.875rem;
  margin: 0;
  opacity: 0.7;
}

/* Empty State */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
}

.empty-icon {
  color: rgba(255, 255, 255, 0.5);
  margin-bottom: 1.5rem;
}

.empty-state h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 0.75rem 0;
  color: rgba(255, 255, 255, 0.9);
}

.empty-state p {
  font-size: 1rem;
  margin: 0;
  opacity: 0.7;
  max-width: 400px;
  line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .file-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.25rem;
  }
}

@media (max-width: 768px) {
  .file-grid {
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 1rem;
    padding: 0.25rem;
  }
  
  .file-grid-header {
    margin-bottom: 1rem;
    padding: 0 0.25rem;
  }
  
  .file-grid-header h3 {
    font-size: 1rem;
  }
}

@media (max-width: 640px) {
  .file-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }
  
  .loading-state,
  .error-state,
  .empty-state {
    padding: 3rem 1rem;
  }
  
  .empty-state h3 {
    font-size: 1.25rem;
  }
  
  .empty-state p {
    font-size: 0.875rem;
  }
}

/* Animation for grid items */
.file-grid .file-card {
  animation: fadeInUp 0.3s ease-out;
}

.file-grid .file-card:nth-child(1) { animation-delay: 0.05s; }
.file-grid .file-card:nth-child(2) { animation-delay: 0.1s; }
.file-grid .file-card:nth-child(3) { animation-delay: 0.15s; }
.file-grid .file-card:nth-child(4) { animation-delay: 0.2s; }
.file-grid .file-card:nth-child(5) { animation-delay: 0.25s; }
.file-grid .file-card:nth-child(6) { animation-delay: 0.3s; }

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Hover effects for better interactivity */
.file-grid:hover .file-card:not(:hover) {
  opacity: 0.7;
  transform: scale(0.98);
}

.file-grid .file-card:hover {
  opacity: 1;
  transform: translateY(-8px) scale(1.02);
  z-index: 10;
}
