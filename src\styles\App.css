* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f5f5f5;
  color: #202124;
}

.app {
  min-height: 100vh;
}

/* Media queries globales */
@media (max-width: 768px) {
  .loading-container {
    padding: 10px;
  }
  
  .loading-spinner {
    width: 25px;
    height: 25px;
  }
}

@media (max-width: 480px) {
  body {
    font-size: 14px;
  }
}


