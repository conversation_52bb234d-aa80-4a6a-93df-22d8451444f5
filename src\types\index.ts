// Tipos para la aplicación Cloud Drive

export interface User {
  uid: string;
  email: string;
  displayName?: string;
}

export interface UserData {
  name?: string;
  email: string;
}

export interface DriveFile {
  id: string;
  name: string;
  url: string;
  folderId: string | null;
  createdAt: any; // Firebase Timestamp
  ownerId: string;
  ownerEmail: string;
  size: number;
  type: string;
  extension: string;
  isShared?: boolean;
}

export interface SharedFile extends DriveFile {
  fileId: string;
  fileName: string;
  fileUrl: string;
  ownerName: string;
  permission: 'view' | 'edit';
  sharedAt: any; // Firebase Timestamp
  isShared: true;
}

export interface Folder {
  id: string;
  name: string;
  createdAt: any; // Firebase Timestamp
}

export type ViewMode = 'my-files' | 'shared-with-me';
export type FilterType = 'all' | 'image' | 'document' | 'video' | 'audio' | 'other';
export type SortBy = 'name' | 'size' | 'createdAt' | 'type';
export type SortDirection = 'asc' | 'desc';
export type Permission = 'view' | 'edit';

export interface SearchAndFilterState {
  searchTerm: string;
  filterType: FilterType;
  sortBy: SortBy;
  sortDirection: SortDirection;
}

export interface DriveState {
  selectedFolderId: string | null;
  viewMode: ViewMode;
  isUploading: boolean;
  isCreatingFolder: boolean;
  selectedFileForShare: DriveFile | null;
  showShareModal: boolean;
  selectedFileForView: DriveFile | null;
  showSidebar: boolean;
}

// Props interfaces
export interface DriveProps {
  user: User;
  userData: UserData;
}

export interface FileListProps {
  files: DriveFile[];
  onDelete: (file: DriveFile) => void;
  onShare?: (file: DriveFile) => void;
  onView: (file: DriveFile) => void;
  loading: boolean;
  error: string | null;
  emptyMessage: string;
}

export interface FolderListProps {
  folders: Folder[];
  selectedFolderId: string | null;
  onSelectFolder: (folderId: string | null) => void;
  onDeleteFolder: (folderId: string) => void;
  loading: boolean;
  error: string | null;
}

export interface UploadFormProps {
  onUpload: (file: File) => void;
  selectedFolderId: string | null;
  isUploading: boolean;
}

export interface CreateFolderFormProps {
  onCreateFolder: (folderName: string) => void;
  isCreating: boolean;
}

export interface ShareModalProps {
  file: DriveFile;
  onShare: (file: DriveFile, email: string, permission: Permission) => Promise<any>;
  onClose: () => void;
}

export interface FileViewerProps {
  file: DriveFile;
  onClose: () => void;
}

export interface SearchAndFilterProps {
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  filterType: FilterType;
  setFilterType: (type: FilterType) => void;
  sortBy: SortBy;
  setSortBy: (sort: SortBy) => void;
  sortDirection: SortDirection;
  setSortDirection: (direction: SortDirection) => void;
}
