/* Tab Navigation Styles */
.tab-navigation {
  position: relative;
  margin-bottom: 2rem;
}

.tab-list {
  display: flex;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 1rem;
  padding: 0.5rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.tab-button {
  flex: 1;
  background: none;
  border: none;
  padding: 1rem 1.5rem;
  border-radius: 0.75rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 1rem;
  color: rgba(255, 255, 255, 0.7);
  position: relative;
  overflow: hidden;
}

.tab-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 0.75rem;
}

.tab-button:hover::before {
  opacity: 1;
}

.tab-button.active {
  background: rgba(255, 255, 255, 0.95);
  color: #374151;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.tab-button.active::before {
  opacity: 0;
}

.tab-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 0.5rem;
  background: linear-gradient(135deg, #4f46e5, #7c3aed);
  color: white;
  transition: all 0.3s ease;
}

.tab-button:not(.active) .tab-icon {
  background: rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.8);
}

.tab-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  text-align: left;
}

.tab-label {
  font-weight: 600;
  font-size: 1rem;
  line-height: 1.2;
}

.tab-description {
  font-size: 0.875rem;
  opacity: 0.7;
  margin-top: 0.25rem;
  line-height: 1.3;
}

.tab-button.active .tab-description {
  opacity: 0.6;
}

/* Responsive Design */
@media (max-width: 768px) {
  .tab-list {
    flex-direction: column;
    gap: 0.25rem;
  }
  
  .tab-button {
    padding: 0.75rem 1rem;
    gap: 0.75rem;
  }
  
  .tab-icon {
    width: 2rem;
    height: 2rem;
  }
  
  .tab-label {
    font-size: 0.9rem;
  }
  
  .tab-description {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .tab-button {
    flex-direction: column;
    text-align: center;
    padding: 1rem 0.75rem;
    gap: 0.5rem;
  }
  
  .tab-content {
    align-items: center;
    text-align: center;
  }
  
  .tab-description {
    display: none;
  }
}

/* Animation for tab switching */
@keyframes tabSlide {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.tab-button.active {
  animation: tabSlide 0.3s ease-out;
}

/* Hover effects */
.tab-button:hover:not(.active) {
  color: rgba(255, 255, 255, 0.9);
  transform: translateY(-1px);
}

.tab-button:hover:not(.active) .tab-icon {
  background: rgba(255, 255, 255, 0.3);
  color: white;
}

/* Focus styles for accessibility */
.tab-button:focus {
  outline: 2px solid rgba(79, 70, 229, 0.5);
  outline-offset: 2px;
}

.tab-button:focus:not(:focus-visible) {
  outline: none;
}
