import React, { useState, useMemo, useEffect } from "react";
import { signOut } from "firebase/auth";
import { auth } from "../firebase";
import { useFiles, useSharedWithMe } from "../hooks/useFiles";
import { useFolders } from "../hooks/useFolders";
import { uploadFile, deleteFile } from "../services/fileService";
import { createFolder, deleteFolder } from "../services/folderService";
import { shareFile } from "../services/shareService";
import { SUCCESS_MESSAGES } from "../constants/permissions";

// Components
import FileList from "../components/FileList";
import FolderList from "../components/FolderList";
import UploadForm from "../components/UploadForm";
import CreateFolderForm from "../components/CreateFolderForm";
import ShareModal from "../components/ShareModal";
import SearchAndFilter from "../components/SearchAndFilter";
import FileViewer from "../components/FileViewer";

// Types
import type {
  DriveProps,
  DriveFile,
  SharedFile,
  ViewMode,
  FilterType,
  SortBy,
  SortDirection,
  Permission,
  DriveState,
  SearchAndFilterState
} from "../types";

// Styles
import "../styles/Drive.css";

const DrivePage: React.FC<DriveProps> = ({ user, userData }) => {
  // Main state
  const [driveState, setDriveState] = useState<DriveState>({
    selectedFolderId: null,
    viewMode: "my-files",
    isUploading: false,
    isCreatingFolder: false,
    selectedFileForShare: null,
    showShareModal: false,
    selectedFileForView: null,
    showSidebar: window.innerWidth > 992
  });

  // Search and filter state
  const [searchAndFilter, setSearchAndFilter] = useState<SearchAndFilterState>({
    searchTerm: "",
    filterType: "all",
    sortBy: "name",
    sortDirection: "asc"
  });

  // Custom hooks
  const { folders, loading: loadingFolders, error: foldersError } = useFolders(user);
  const { files, loading: loadingFiles, error: filesError } = useFiles(user, driveState.selectedFolderId);
  const { sharedFiles, loading: loadingShared, error: sharedError } = useSharedWithMe(user);

  const displayName = userData?.name || user.email;

  // Memoized filtered and sorted files
  const filteredAndSortedFiles = useMemo(() => {
    let result = [...files];
    
    if (searchAndFilter.searchTerm) {
      const searchLower = searchAndFilter.searchTerm.toLowerCase();
      result = result.filter(file => 
        file.name.toLowerCase().includes(searchLower)
      );
    }
    
    if (searchAndFilter.filterType !== "all") {
      result = result.filter(file => file.type === searchAndFilter.filterType);
    }
    
    result.sort((a, b) => {
      let valueA: any, valueB: any;
      
      if (searchAndFilter.sortBy === "name") {
        valueA = a.name.toLowerCase();
        valueB = b.name.toLowerCase();
      } else if (searchAndFilter.sortBy === "size") {
        valueA = a.size || 0;
        valueB = b.size || 0;
      } else if (searchAndFilter.sortBy === "createdAt") {
        valueA = a.createdAt ? (a.createdAt.toDate ? a.createdAt.toDate() : new Date(a.createdAt)) : new Date(0);
        valueB = b.createdAt ? (b.createdAt.toDate ? b.createdAt.toDate() : new Date(b.createdAt)) : new Date(0);
      } else if (searchAndFilter.sortBy === "type") {
        valueA = a.type || "";
        valueB = b.type || "";
      }
      
      if (searchAndFilter.sortDirection === "asc") {
        return valueA > valueB ? 1 : -1;
      } else {
        return valueA < valueB ? 1 : -1;
      }
    });
    
    return result;
  }, [files, searchAndFilter]);

  // Memoized filtered and sorted shared files
  const filteredAndSortedSharedFiles = useMemo(() => {
    let result = [...sharedFiles];
    
    if (searchAndFilter.searchTerm) {
      const searchLower = searchAndFilter.searchTerm.toLowerCase();
      result = result.filter(file => 
        file.fileName.toLowerCase().includes(searchLower)
      );
    }
    
    if (searchAndFilter.filterType !== "all") {
      result = result.filter(file => file.type === searchAndFilter.filterType);
    }
    
    result.sort((a, b) => {
      let valueA: any, valueB: any;
      
      if (searchAndFilter.sortBy === "name") {
        valueA = a.fileName.toLowerCase();
        valueB = b.fileName.toLowerCase();
      } else if (searchAndFilter.sortBy === "size") {
        valueA = a.size || 0;
        valueB = b.size || 0;
      } else if (searchAndFilter.sortBy === "createdAt") {
        valueA = a.createdAt ? (a.createdAt.toDate ? a.createdAt.toDate() : new Date(a.createdAt)) : new Date(0);
        valueB = b.createdAt ? (b.createdAt.toDate ? b.createdAt.toDate() : new Date(b.createdAt)) : new Date(0);
      } else if (searchAndFilter.sortBy === "type") {
        valueA = a.type || "";
        valueB = b.type || "";
      }
      
      if (searchAndFilter.sortDirection === "asc") {
        return valueA > valueB ? 1 : -1;
      } else {
        return valueA < valueB ? 1 : -1;
      }
    });
    
    return result;
  }, [sharedFiles, searchAndFilter]);

  // Window resize effect
  useEffect(() => {
    const handleResize = () => {
      setDriveState(prev => ({
        ...prev,
        showSidebar: window.innerWidth > 992
      }));
    };
    
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Event handlers
  const handleLogout = async (): Promise<void> => {
    try {
      await signOut(auth);
    } catch (error) {
      console.error("Error al cerrar sesión:", error);
    }
  };

  const handleUpload = async (file: File): Promise<void> => {
    if (!file) return;
    
    setDriveState(prev => ({ ...prev, isUploading: true }));
    try {
      await uploadFile(file, user, driveState.selectedFolderId);
      alert(SUCCESS_MESSAGES.UPLOAD);
    } catch (error: any) {
      console.error("Error al subir archivo:", error);
      alert("Error al subir archivo: " + error.message);
    } finally {
      setDriveState(prev => ({ ...prev, isUploading: false }));
    }
  };

  const handleCreateFolder = async (folderName: string): Promise<void> => {
    if (!folderName.trim()) return;
    
    setDriveState(prev => ({ ...prev, isCreatingFolder: true }));
    try {
      await createFolder(folderName, user);
      alert(SUCCESS_MESSAGES.FOLDER_CREATE);
    } catch (error: any) {
      console.error("Error al crear carpeta:", error);
      alert("Error al crear carpeta: " + error.message);
    } finally {
      setDriveState(prev => ({ ...prev, isCreatingFolder: false }));
    }
  };

  const handleDeleteFile = async (file: DriveFile): Promise<void> => {
    if (!window.confirm(`¿Estás seguro de que quieres eliminar "${file.name || (file as SharedFile).fileName}"?`)) {
      return;
    }
    
    try {
      const message = await deleteFile(file, user);
      alert(message);
    } catch (error: any) {
      console.error("Error al eliminar archivo:", error);
      alert("Error al eliminar archivo: " + error.message);
    }
  };

  const handleDeleteFolder = async (folderId: string): Promise<void> => {
    if (!window.confirm("¿Estás seguro de que quieres eliminar esta carpeta y todo su contenido?")) {
      return;
    }
    
    try {
      const message = await deleteFolder(folderId, user);
      alert(message);
    } catch (error: any) {
      console.error("Error al eliminar carpeta:", error);
      alert("Error al eliminar carpeta: " + error.message);
    }
  };

  const handleShareFile = async (file: DriveFile, email: string, permission: Permission): Promise<any> => {
    try {
      const result = await shareFile(file, email, permission, user);
      alert(result.message || SUCCESS_MESSAGES.SHARE);
      return result;
    } catch (error: any) {
      console.error("Error al compartir archivo:", error);
      alert("Error al compartir archivo: " + error.message);
      throw error;
    }
  };

  // Modal handlers
  const openShareModal = (file: DriveFile): void => {
    setDriveState(prev => ({
      ...prev,
      selectedFileForShare: file,
      showShareModal: true
    }));
  };

  const closeShareModal = (): void => {
    setDriveState(prev => ({
      ...prev,
      selectedFileForShare: null,
      showShareModal: false
    }));
  };

  const openFileViewer = (file: DriveFile): void => {
    setDriveState(prev => ({
      ...prev,
      selectedFileForView: file
    }));
  };

  const closeFileViewer = (): void => {
    setDriveState(prev => ({
      ...prev,
      selectedFileForView: null
    }));
  };

  // State update helpers
  const updateViewMode = (viewMode: ViewMode): void => {
    setDriveState(prev => ({ ...prev, viewMode }));
  };

  const updateSelectedFolder = (folderId: string | null): void => {
    setDriveState(prev => ({ ...prev, selectedFolderId: folderId }));
  };

  const toggleSidebar = (): void => {
    setDriveState(prev => ({ ...prev, showSidebar: !prev.showSidebar }));
  };

  const updateSearchTerm = (searchTerm: string): void => {
    setSearchAndFilter(prev => ({ ...prev, searchTerm }));
  };

  const updateFilterType = (filterType: FilterType): void => {
    setSearchAndFilter(prev => ({ ...prev, filterType }));
  };

  const updateSortBy = (sortBy: SortBy): void => {
    setSearchAndFilter(prev => ({ ...prev, sortBy }));
  };

  const updateSortDirection = (sortDirection: SortDirection): void => {
    setSearchAndFilter(prev => ({ ...prev, sortDirection }));
  };

  return (
    <div className="drive-container">
      <header className="drive-header">
        <h1>Bienvenido, {displayName}</h1>
        <div className="header-actions">
          {window.innerWidth <= 992 && (
            <button 
              className="toggle-sidebar-btn"
              onClick={toggleSidebar}
              aria-label={driveState.showSidebar ? "Ocultar carpetas" : "Mostrar carpetas"}
            >
              {driveState.showSidebar ? "◀ Ocultar" : "▶ Carpetas"}
            </button>
          )}
          <button className="btn logout-btn" onClick={handleLogout}>
            Cerrar sesión
          </button>
        </div>
      </header>

      <div className="view-toggle">
        <button 
          className={`toggle-btn ${driveState.viewMode === "my-files" ? "active" : ""}`}
          onClick={() => updateViewMode("my-files")}
        >
          Mis archivos
        </button>
        <button 
          className={`toggle-btn ${driveState.viewMode === "shared-with-me" ? "active" : ""}`}
          onClick={() => updateViewMode("shared-with-me")}
        >
          Compartidos conmigo
        </button>
      </div>
      
      {driveState.viewMode === "my-files" && (
        <div className="drive-content">
          {(driveState.showSidebar || window.innerWidth > 992) && (
            <div className="sidebar">
              <h3>Carpetas</h3>
              <FolderList 
                folders={folders}
                selectedFolderId={driveState.selectedFolderId}
                onSelectFolder={updateSelectedFolder}
                onDeleteFolder={handleDeleteFolder}
                loading={loadingFolders}
                error={foldersError}
              />
              
              <div className="create-folder-section">
                <h3>Crear carpeta</h3>
                <CreateFolderForm 
                  onCreateFolder={handleCreateFolder}
                  isCreating={driveState.isCreatingFolder}
                />
              </div>
            </div>
          )}
          
          <div className="main-content">
            <h2>
              {driveState.selectedFolderId 
                ? `Archivos en: ${folders.find(f => f.id === driveState.selectedFolderId)?.name || "Carpeta"}`
                : "Archivos en raíz"}
            </h2>
            
            <div className="upload-section">
              <h3>Subir archivo</h3>
              <UploadForm 
                onUpload={handleUpload}
                selectedFolderId={driveState.selectedFolderId}
                isUploading={driveState.isUploading}
              />
            </div>
            
            <div className="files-section">
              <h3>Mis archivos</h3>
              
              <SearchAndFilter 
                searchTerm={searchAndFilter.searchTerm}
                setSearchTerm={updateSearchTerm}
                filterType={searchAndFilter.filterType}
                setFilterType={updateFilterType}
                sortBy={searchAndFilter.sortBy}
                setSortBy={updateSortBy}
                sortDirection={searchAndFilter.sortDirection}
                setSortDirection={updateSortDirection}
              />
              
              <FileList 
                files={filteredAndSortedFiles}
                onDelete={handleDeleteFile}
                onShare={openShareModal}
                onView={openFileViewer}
                loading={loadingFiles}
                error={filesError}
                emptyMessage={
                  searchAndFilter.searchTerm || searchAndFilter.filterType !== "all" 
                    ? "No se encontraron archivos con los filtros aplicados" 
                    : (driveState.selectedFolderId 
                      ? "No hay archivos en esta carpeta" 
                      : "No hay archivos en la raíz")
                }
              />
            </div>
          </div>
        </div>
      )}
      
      {driveState.viewMode === "shared-with-me" && (
        <div className="shared-content">
          <h2>Archivos compartidos conmigo</h2>
          
          <SearchAndFilter 
            searchTerm={searchAndFilter.searchTerm}
            setSearchTerm={updateSearchTerm}
            filterType={searchAndFilter.filterType}
            setFilterType={updateFilterType}
            sortBy={searchAndFilter.sortBy}
            setSortBy={updateSortBy}
            sortDirection={searchAndFilter.sortDirection}
            setSortDirection={updateSortDirection}
          />
          
          <FileList 
            files={filteredAndSortedSharedFiles}
            onDelete={handleDeleteFile}
            onView={openFileViewer}
            loading={loadingShared}
            error={sharedError}
            emptyMessage={
              searchAndFilter.searchTerm || searchAndFilter.filterType !== "all" 
                ? "No se encontraron archivos compartidos con los filtros aplicados" 
                : "No hay archivos compartidos contigo"
            }
          />
        </div>
      )}
      
      {/* Modal para compartir archivos */}
      {driveState.showShareModal && driveState.selectedFileForShare && (
        <ShareModal 
          file={driveState.selectedFileForShare}
          onShare={handleShareFile}
          onClose={closeShareModal}
        />
      )}
      
      {/* Visor de archivos */}
      {driveState.selectedFileForView && (
        <FileViewer 
          file={driveState.selectedFileForView}
          onClose={closeFileViewer}
        />
      )}
    </div>
  );
};

export default DrivePage;
